// Builder Canvas Component
// Main canvas area for drag-and-drop page building

'use client'

import React, { useRef, useCallback, useEffect, useState } from 'react'
// import { DndProvider, useDrop, useDrag } from 'react-dnd'
// import { HTML5Backend } from 'react-dnd-html5-backend'
// import { Responsive, WidthProvider, Layout } from 'react-grid-layout'
import { useBuilderStore } from '../store/builder-store'
// import { BlockRenderer } from './block-renderer'

// Simple block renderer component
function SimpleBlockRenderer({ block }: { block: any }) {
  return (
    <div className="w-full h-full p-4 bg-gray-50 border rounded">
      <div className="text-sm font-medium text-gray-700">{block.name || block.type}</div>
      <div className="text-xs text-gray-500 mt-1">
        {block.content?.text || `${block.type} block`}
      </div>
    </div>
  )
}
// import { DropZone } from './drop-zone'
// import { SelectionOverlay } from './selection-overlay'
// import { GridOverlay } from './grid-overlay'
// import { DevicePreview } from './device-preview'
import { cn } from '@/lib/utils'

// const ResponsiveGridLayout = WidthProvider(Responsive)

interface BuilderCanvasProps {
  className?: string
}

export function BuilderCanvas({ className }: BuilderCanvasProps) {
  const canvasRef = useRef<HTMLDivElement>(null)

  const {
    currentPage,
    selectedBlocks,
    hoveredBlock,
    isPreviewMode,
    currentDevice,
    zoom,
    selectBlock,
    setHoveredBlock
  } = useBuilderStore()

  // Handle block click
  const handleBlockClick = useCallback((blockId: string, event: React.MouseEvent) => {
    event.stopPropagation()

    if (isPreviewMode) return

    const isMultiSelect = event.ctrlKey || event.metaKey
    selectBlock(blockId, isMultiSelect)
  }, [isPreviewMode, selectBlock])

  // Handle block hover
  const handleBlockHover = useCallback((blockId: string | null) => {
    if (!isPreviewMode) {
      setHoveredBlock(blockId)
    }
  }, [isPreviewMode, setHoveredBlock])

  // Handle canvas click (deselect all)
  const handleCanvasClick = useCallback((event: React.MouseEvent) => {
    if (event.target === event.currentTarget) {
      selectBlock('', false) // Clear selection
    }
  }, [selectBlock])

  if (!currentPage) {
    return (
      <div className={cn(
        "flex-1 flex items-center justify-center bg-gray-50",
        className
      )}>
        <div className="text-center">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Page Selected</h3>
          <p className="text-gray-500">Create a new page or select an existing one to start building.</p>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("flex-1 relative overflow-hidden", className)}>
      <div
        ref={canvasRef}
        className="relative w-full h-full bg-white transition-all duration-200"
        onClick={handleCanvasClick}
        style={{
          minHeight: '100vh',
          transform: `scale(${zoom / 100})`,
          transformOrigin: 'top left'
        }}
      >
        {/* Blocks */}
        {currentPage?.blocks.map((block: any) => (
          <div
            key={block.id}
            className={cn(
              "relative group transition-all duration-200 border-2 border-dashed border-transparent",
              {
                'border-blue-500': selectedBlocks.includes(block.id),
                'border-blue-300': hoveredBlock === block.id && !selectedBlocks.includes(block.id),
                'opacity-50': !block.isVisible
              }
            )}
            style={{
              position: 'absolute',
              left: `${(block.position.x / 12) * 100}%`,
              top: `${block.position.y * 60}px`,
              width: `${(block.position.w / 12) * 100}%`,
              height: `${block.position.h * 60}px`,
              minHeight: '60px'
            }}
            onClick={(e) => handleBlockClick(block.id, e)}
            onMouseEnter={() => handleBlockHover(block.id)}
            onMouseLeave={() => handleBlockHover(null)}
          >
            {/* Block Renderer */}
            <SimpleBlockRenderer block={block} />
          </div>
        ))}

        {/* Empty State */}
        {(!currentPage?.blocks || currentPage.blocks.length === 0) && (
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="text-center">
              <div className="text-gray-300 mb-4">
                <svg className="w-20 h-20 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <h3 className="text-xl font-medium text-gray-400 mb-2">Start Building</h3>
              <p className="text-gray-300">Add blocks to begin creating your page.</p>
            </div>
          </div>
        )}
      </div>

      {/* Canvas Info */}
      {!isPreviewMode && currentPage && (
        <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-sm border px-3 py-2 text-sm text-gray-600">
          <div className="flex items-center space-x-4">
            <span>Zoom: {zoom}%</span>
            <span>Device: {currentDevice}</span>
            <span>Blocks: {currentPage.blocks.length}</span>
            {selectedBlocks.length > 0 && (
              <span>Selected: {selectedBlocks.length}</span>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default BuilderCanvas
