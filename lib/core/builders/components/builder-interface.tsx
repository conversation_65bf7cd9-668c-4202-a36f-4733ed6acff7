// Builder Interface Component
// Main interface that combines all builder components

'use client'

import React, { useEffect } from 'react'
import { useBuilderStore } from '../store/builder-store'
import { useBlockLibraryStore } from '../store/block-library-store'
import { useTemplateStore } from '../store/template-store'
import { BuilderCanvas } from './builder-canvas'
// import { BuilderSidebar } from './builder-sidebar'
// import { BuilderToolbar } from './builder-toolbar'
// import { BuilderRightPanel } from './builder-right-panel'
// import { BuilderStatusBar } from './builder-status-bar'
import { cn } from '@/lib/utils'

interface BuilderInterfaceProps {
  className?: string
  pageId?: string
  onSave?: (pageData: any) => void
  onPublish?: (pageData: any) => void
  onPreview?: (pageData: any) => void
}

export function BuilderInterface({
  className,
  pageId,
  onSave,
  onPublish,
  onPreview
}: BuilderInterfaceProps) {
  const {
    currentPage,
    sidebarCollapsed,
    rightPanelOpen,
    isPreviewMode,
    isLoading,
    error,
    setCurrentPage,
    createPage
  } = useBuilderStore()

  const { blockDefinitions } = useBlockLibraryStore()
  const { templates } = useTemplateStore()

  // Initialize page if pageId is provided
  useEffect(() => {
    if (pageId && !currentPage) {
      // Load page from API or create new one
      // This is a simplified implementation
      createPage({
        title: 'New Page',
        slug: 'new-page'
      })
    }
  }, [pageId, currentPage, createPage])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Prevent shortcuts when typing in inputs
      if (
        event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement ||
        (event.target as HTMLElement)?.contentEditable === 'true'
      ) {
        return
      }

      const { ctrlKey, metaKey, key } = event
      const isModifier = ctrlKey || metaKey

      if (isModifier) {
        switch (key.toLowerCase()) {
          case 's':
            event.preventDefault()
            if (currentPage && onSave) {
              onSave(currentPage)
            }
            break
          case 'z':
            event.preventDefault()
            // Handle undo
            break
          case 'y':
            event.preventDefault()
            // Handle redo
            break
          case 'c':
            event.preventDefault()
            // Handle copy
            break
          case 'v':
            event.preventDefault()
            // Handle paste
            break
          case 'x':
            event.preventDefault()
            // Handle cut
            break
          case 'd':
            event.preventDefault()
            // Handle duplicate
            break
        }
      } else {
        switch (key) {
          case 'Delete':
          case 'Backspace':
            event.preventDefault()
            // Handle delete selected blocks
            break
          case 'Escape':
            event.preventDefault()
            // Clear selection
            break
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [currentPage, onSave])

  // Auto-save functionality
  useEffect(() => {
    if (!currentPage || !onSave) return

    const autoSaveInterval = setInterval(() => {
      onSave(currentPage)
    }, 30000) // Auto-save every 30 seconds

    return () => clearInterval(autoSaveInterval)
  }, [currentPage, onSave])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading builder...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50">
        <div className="text-center max-w-md">
          <div className="text-red-500 text-4xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Builder Error</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Reload Builder
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("flex flex-col h-screen bg-gray-50", className)}>
      {/* Top Toolbar */}
      <div className="bg-white border-b border-gray-200 px-4 py-2 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-lg font-semibold">Page Builder</h1>
          {currentPage && (
            <span className="text-sm text-gray-500">{currentPage.title}</span>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => currentPage && onSave?.(currentPage)}
            className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
          >
            Save
          </button>
          <button
            onClick={() => currentPage && onPublish?.(currentPage)}
            className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
          >
            Publish
          </button>
          <button
            onClick={() => currentPage && onPreview?.(currentPage)}
            className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
          >
            Preview
          </button>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex flex-1 overflow-hidden">
        {/* Left Sidebar */}
        {!sidebarCollapsed && (
          <div className="w-80 border-r border-gray-200 bg-white p-4">
            <h3 className="font-medium mb-4">Block Library</h3>
            <div className="space-y-2">
              <div className="p-2 border rounded cursor-pointer hover:bg-gray-50">
                Heading Block
              </div>
              <div className="p-2 border rounded cursor-pointer hover:bg-gray-50">
                Text Block
              </div>
              <div className="p-2 border rounded cursor-pointer hover:bg-gray-50">
                Button Block
              </div>
            </div>
          </div>
        )}

        {/* Canvas Area */}
        <div className="flex-1 flex flex-col">
          <BuilderCanvas className="flex-1" />

          {/* Status Bar */}
          <div className="border-t border-gray-200 bg-white px-4 py-2 text-sm text-gray-600">
            {currentPage ? `${currentPage.blocks.length} blocks` : 'No page selected'}
          </div>
        </div>

        {/* Right Panel */}
        {rightPanelOpen && !isPreviewMode && (
          <div className="w-80 border-l border-gray-200 bg-white p-4">
            <h3 className="font-medium mb-4">Settings</h3>
            <div className="text-sm text-gray-500">
              Select a block to edit its properties
            </div>
          </div>
        )}
      </div>

      {/* Floating Elements */}
      {isPreviewMode && (
        <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50">
          <div className="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            <span>Preview Mode</span>
            <button
              onClick={() => useBuilderStore.getState().setPreviewMode(false)}
              className="ml-2 text-blue-200 hover:text-white"
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {/* Keyboard Shortcuts Help */}
      <div className="fixed bottom-4 right-4 z-40">
        <button
          className="bg-gray-800 text-white p-2 rounded-full shadow-lg hover:bg-gray-700 transition-colors"
          title="Keyboard Shortcuts"
          onClick={() => {
            // Show keyboard shortcuts modal
          }}
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </button>
      </div>
    </div>
  )
}

export default BuilderInterface
